import { Meteor } from 'meteor/meteor';
import { Accounts } from 'meteor/accounts-base';
import { PostsCollection } from '/imports/api/posts';
import { CoverPhotosCollection } from '/imports/api/coverPhotos';
import './bunnyStorage.js'; // Import server-only Bunny.net storage functions
import '/imports/api/uploads.js'; // Import Bunny.net upload methods
import { check } from 'meteor/check';
import 'meteor/accounts-password';
import './fixtures.js';

Meteor.startup(async () => {
  // Configure accounts
  Accounts.config({
    sendVerificationEmail: false,
    forbidClientAccountCreation: false,
  });

  // Set up image proxy route for Bunny.net authenticated access
  const { WebApp } = require('meteor/webapp');

  WebApp.connectHandlers.use('/images', async (req, res, next) => {
    try {
      // Extract the image key from the URL path
      const imageKey = req.url.substring(1); // Remove leading slash

      if (!imageKey) {
        res.writeHead(404, { 'Content-Type': 'text/plain' });
        res.end('Image not found');
        return;
      }

      // Download the image from Bunny.net with authentication
      const imageBuffer = await global.BunnyStorage.downloadFromBunny(imageKey);

      // Ensure we have a valid buffer
      if (!Buffer.isBuffer(imageBuffer)) {
        throw new Error('Invalid image data format received from Bunny.net');
      }

      // Determine content type based on file extension
      const extension = imageKey.split('.').pop().toLowerCase();
      let contentType = 'image/jpeg'; // default

      switch (extension) {
        case 'png':
          contentType = 'image/png';
          break;
        case 'gif':
          contentType = 'image/gif';
          break;
        case 'webp':
          contentType = 'image/webp';
          break;
        case 'jpg':
        case 'jpeg':
          contentType = 'image/jpeg';
          break;
      }

      // Set appropriate headers and send response
      res.writeHead(200, {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000',
        'Content-Length': imageBuffer.length
      });
      res.end(imageBuffer);

    } catch (error) {
      console.error('Error serving image:', error);
      if (!res.headersSent) {
        res.writeHead(404, { 'Content-Type': 'text/plain' });
        res.end('Image not found');
      }
    }
  });
});



// Methods
Meteor.methods({
  'users.updateBio'(bio) {
    check(bio, String);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in to update your bio');
    }

    return Meteor.users.updateAsync(this.userId, {
      $set: {
        'profile.bio': bio
      }
    });
  },

  'users.updateProfile'(profileData) {
    check(profileData, {
      fullname: String,
      bio: String,
      location: String,
      work: String,
      education: String,
      dob: String
    });

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in to update your profile');
    }

    // Validate fullname
    if (!profileData.fullname.trim()) {
      throw new Meteor.Error('invalid-data', 'Full name is required');
    }

    // Validate date format if provided
    if (profileData.dob && profileData.dob.length > 0) {
      const dateParts = profileData.dob.split('.');
      if (dateParts.length !== 3 || profileData.dob.length !== 10) {
        throw new Meteor.Error('invalid-data', 'Please enter date in dd.mm.yyyy format');
      }

      const day = parseInt(dateParts[0], 10);
      const month = parseInt(dateParts[1], 10);
      const year = parseInt(dateParts[2], 10);

      if (isNaN(day) || isNaN(month) || isNaN(year) ||
          day < 1 || day > 31 || month < 1 || month > 12 || year < 1900 || year > new Date().getFullYear()) {
        throw new Meteor.Error('invalid-data', 'Please enter a valid date');
      }
    }

    return Meteor.users.updateAsync(this.userId, {
      $set: {
        'profile.fullname': profileData.fullname.trim(),
        'profile.bio': profileData.bio.trim(),
        'profile.location': profileData.location.trim(),
        'profile.work': profileData.work.trim(),
        'profile.education': profileData.education.trim(),
        'profile.dob': profileData.dob.trim()
      }
    });
  }
});

// Publish user data
Meteor.publish("userData", function () {
  if (this.userId) {
    return Meteor.users.find(
      { _id: this.userId },
      { fields: { emails: 1, profile: 1 } }
    );
  } else {
    this.ready();
  }
});

// Publish all users for profile lookup
Meteor.publish("allUsers", function () {
  return Meteor.users.find({}, {
    fields: {
      emails: 1,
      profile: 1,
      createdAt: 1
    }
  });
});
