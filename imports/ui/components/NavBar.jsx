import React, { useState, useRef, useEffect } from 'react';
import { Meteor } from 'meteor/meteor';
import { getImageUrl, getUserInitials, getDisplayName } from '/imports/utils/profileUtils';

export const NavBar = ({ currentUser, onNavigate, onLogout, onThemeToggle, currentTheme }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const dropdownRef = useRef(null);
  const notificationRef = useRef(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowUserDropdown(false);
      }
      if (notificationRef.current && !notificationRef.current.contains(event.target)) {
        setShowNotifications(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // TODO: Implement search functionality
      console.log('Searching for:', searchQuery);
    }
  };

  const handleLogoClick = () => {
    // Navigate to user's own profile
    if (currentUser?.profile?.fullname) {
      const profileUrl = currentUser.profile.fullname
        .toLowerCase()
        .trim()
        .replace(/\s+/g, '')
        .replace(/[^a-z0-9]/g, '');
      onNavigate(`/profile/${profileUrl}`);
    }
  };

  const userInitials = getUserInitials(currentUser?.profile?.fullname);
  const displayName = getDisplayName(currentUser?.profile?.fullname);

  return (
    <nav className="navbar">
      <div className="navbar-container">
        {/* Left Section - Logo and Search */}
        <div className="navbar-left">
          <div className="navbar-logo" onClick={handleLogoClick}>
            <div className="logo-icon">R</div>
            <span className="logo-text">Roylia</span>
          </div>
          
          <form className="navbar-search" onSubmit={handleSearch}>
            <div className="search-container">
              <svg className="search-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
              </svg>
              <input
                type="text"
                placeholder="Search Roylia"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="search-input"
              />
            </div>
          </form>
        </div>

        {/* Right Section - Icons and Profile */}
        <div className="navbar-right">
          {/* Theme Toggle */}
          <div className="navbar-item">
            <button
              className="navbar-icon-btn theme-toggle-nav"
              onClick={() => onThemeToggle && onThemeToggle()}
              title="Toggle theme"
            >
              {currentTheme === 'light' ? '🌙' : '☀️'}
            </button>
          </div>

          {/* Notifications */}
          <div className="navbar-item" ref={notificationRef}>
            <button 
              className="navbar-icon-btn"
              onClick={() => setShowNotifications(!showNotifications)}
              title="Notifications"
            >
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
              </svg>
              <span className="notification-badge">3</span>
            </button>
            
            {showNotifications && (
              <div className="dropdown notifications-dropdown">
                <div className="dropdown-header">
                  <h3>Notifications</h3>
                </div>
                <div className="dropdown-content">
                  <div className="notification-item">
                    <div className="notification-text">
                      <strong>John Doe</strong> liked your post
                    </div>
                    <div className="notification-time">2 hours ago</div>
                  </div>
                  <div className="notification-item">
                    <div className="notification-text">
                      <strong>Jane Smith</strong> commented on your photo
                    </div>
                    <div className="notification-time">5 hours ago</div>
                  </div>
                  <div className="notification-item">
                    <div className="notification-text">
                      <strong>Mike Johnson</strong> started following you
                    </div>
                    <div className="notification-time">1 day ago</div>
                  </div>
                </div>
                <div className="dropdown-footer">
                  <button className="see-all-btn">See All Notifications</button>
                </div>
              </div>
            )}
          </div>

          {/* User Profile Dropdown */}
          <div className="navbar-item" ref={dropdownRef}>
            <button 
              className="navbar-profile-btn"
              onClick={() => setShowUserDropdown(!showUserDropdown)}
              title={displayName}
            >
              {currentUser?.profile?.profilePicture ? (
                <img
                  src={getImageUrl(currentUser.profile.profilePicture)}
                  alt={displayName}
                  className="profile-avatar"
                />
              ) : (
                <div className="profile-avatar-placeholder">
                  <span>{userInitials}</span>
                </div>
              )}
              <svg className="dropdown-arrow" viewBox="0 0 24 24" fill="currentColor">
                <path d="M7 10l5 5 5-5z"/>
              </svg>
            </button>
            
            {showUserDropdown && (
              <div className="dropdown user-dropdown">
                <div className="dropdown-header">
                  <div className="user-info">
                    {currentUser?.profile?.profilePicture ? (
                      <img
                        src={getImageUrl(currentUser.profile.profilePicture)}
                        alt={displayName}
                        className="user-avatar"
                      />
                    ) : (
                      <div className="user-avatar-placeholder">
                        <span>{userInitials}</span>
                      </div>
                    )}
                    <div className="user-details">
                      <div className="user-name">{displayName}</div>
                      <div className="user-email">{currentUser?.emails?.[0]?.address}</div>
                    </div>
                  </div>
                </div>
                <div className="dropdown-content">
                  <button className="dropdown-item" onClick={handleLogoClick}>
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                    View Profile
                  </button>
                  <button className="dropdown-item" onClick={() => console.log('Settings clicked - TODO: Implement settings')}>
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                    </svg>
                    Settings
                  </button>
                  <div className="dropdown-divider"></div>
                  <button className="dropdown-item logout-item" onClick={onLogout}>
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                    </svg>
                    Logout
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};
