import React, { useEffect, useState } from 'react';
import { useTracker } from 'meteor/react-meteor-data';
import { Meteor } from 'meteor/meteor';
import { Landing } from './Landing.jsx';
import { Register } from './Register.jsx';
import { UserProfile } from './UserProfile.jsx';
import { NavBar } from './components/NavBar.jsx';
import { generateProfileUrl } from '../utils/profileUtils.js';

export const App = () => {
  // Initialize theme from localStorage or default to 'light'
  const [theme, setTheme] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('roylia-theme') || 'light';
    }
    return 'light';
  });
  const [page, setPage] = useState('landing');
  const [currentRoute, setCurrentRoute] = useState(window.location.pathname);

  // Track user authentication state
  const { user, isLoading } = useTracker(() => {
    const user = Meteor.user();
    const isLoading = Meteor.loggingIn();
    return { user, isLoading };
  }, []);

  useEffect(() => {
    document.body.dataset.theme = theme;
  }, [theme]);

  // Handle browser navigation
  useEffect(() => {
    const handlePopState = () => {
      setCurrentRoute(window.location.pathname);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  // Navigation helper
  const navigateTo = (path) => {
    window.history.pushState({}, '', path);
    setCurrentRoute(path);
  };

  const toggleTheme = (newTheme) => {
    let selectedTheme;
    if (newTheme) {
      selectedTheme = newTheme;
    } else {
      selectedTheme = theme === 'light' ? 'dark' : 'light';
    }

    // Update state
    setTheme(selectedTheme);

    // Save to localStorage for persistence
    if (typeof window !== 'undefined') {
      localStorage.setItem('roylia-theme', selectedTheme);
    }
  };

  const handleLogout = () => {
    Meteor.logout();
    setPage('landing');
    navigateTo('/');
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="app">
        <div className="landing">
          <h1 className="title brand">Roylia</h1>
          <p className="tagline">Loading...</p>
        </div>
      </div>
    );
  }

  // Show user content if logged in
  if (user) {
    // Parse current route
    const pathParts = currentRoute.split('/').filter(part => part);

    // Check if it's a profile route
    if (pathParts[0] === 'profile' && pathParts[1]) {
      const profileUrl = pathParts[1];
      return (
        <div className="app app-with-navbar">
          <NavBar
            currentUser={user}
            onNavigate={navigateTo}
            onLogout={handleLogout}
          />
          <UserProfile
            profileUrl={profileUrl}
            currentUser={user}
            onThemeToggle={toggleTheme}
            currentTheme={theme}
          />
        </div>
      );
    }

    // Default dashboard/home view - redirect to user's profile
    const userProfileUrl = generateProfileUrl(user.profile?.fullname);
    if (userProfileUrl) {
      navigateTo(`/profile/${userProfileUrl}`);
      return null;
    }

    // Fallback if no profile URL can be generated
    return (
      <div className="app app-with-navbar">
        <NavBar
          currentUser={user}
          onNavigate={navigateTo}
          onLogout={handleLogout}
        />
        <div className="dashboard">
          <div className="welcome-section">
            <h1 className="title brand">Roylia</h1>
            <h2>Welcome, {user.profile?.fullname || user.emails?.[0]?.address}!</h2>
            <p>The social network for the mentally royal.</p>
            <p>Please update your profile to continue.</p>
            <button className="btn" onClick={handleLogout}>Logout</button>
            <button className="theme-toggle" onClick={toggleTheme} aria-label="Toggle theme">
              {theme === 'light' ? '🌙' : '☀️'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Show login/register pages if user is not logged in
  return (
    <div className="app">
      {page === 'landing' && (
        <Landing
          goToRegister={() => setPage('register')}
          theme={theme}
          onThemeToggle={toggleTheme}
        />
      )}
      {page === 'register' && (
        <Register
          goToLanding={() => setPage('landing')}
          theme={theme}
          onThemeToggle={toggleTheme}
        />
      )}
    </div>
  );
};
